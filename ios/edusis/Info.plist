<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>edu-sis</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
		<key>CFBundleShortVersionString</key>
		<string>1.0.0</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.edunovaasia.edusis</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>1</string>
		<key>LSMinimumSystemVersion</key>
		<string>12.0</string>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
			<key>NSAllowsLocalNetworking</key>
			<true />
			<key>NSExceptionDomains</key>
			<dict>
				<key>sis.bfi.edu.mm</key>
				<dict>
					<key>NSExceptionAllowsInsecureHTTPLoads</key>
					<true />
					<key>NSIncludesSubdomains</key>
					<true />
				</dict>
				<key>example.com</key>
				<dict>
					<key>NSExceptionAllowsInsecureHTTPLoads</key>
					<true />
					<key>NSIncludesSubdomains</key>
					<true />
				</dict>
			</dict>
		</dict>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
			<string>processing</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>SplashScreen</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>arm64</string>
		</array>
		<key>UIRequiresFullScreen</key>
		<false />
		<key>UIStatusBarStyle</key>
		<string>UIStatusBarStyleDefault</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIUserInterfaceStyle</key>
		<string>Light</string>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
	</dict>
</plist>