import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faArrowLeft, faClipboardList } from '@fortawesome/free-solid-svg-icons';
import { useScreenOrientation } from '../hooks/useScreenOrientation';

export default function AssignmentsScreen({ navigation, route }) {
  const [screenData, setScreenData] = useState(Dimensions.get('window'));
  const { studentName, authCode } = route.params || {};
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);

  // Enable rotation for this screen
  useScreenOrientation(true);

  // Listen for orientation changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });

    return () => subscription?.remove();
  }, []);

  const isLandscape = screenData.width > screenData.height;

  useEffect(() => {
    if (authCode) {
      fetchAssignmentsData();
    }
  }, [authCode]);

  const fetchAssignmentsData = async () => {
    if (!authCode) {
      Alert.alert('Error', 'Authentication code is missing');
      return;
    }

    setLoading(true);
    try {
      console.log('Fetching assignments with authCode:', authCode);
      const url = `https://sis.bfi.edu.mm/mobile-api/get-student-homework?authCode=${authCode}`;
      console.log('Request URL:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const data = await response.json();
        console.log('Raw assignments data:', JSON.stringify(data, null, 2));
        
        // Log the structure of the data
        console.log('Data type:', typeof data);
        console.log('Data keys:', Object.keys(data));
        
        if (Array.isArray(data)) {
          console.log('Data is an array with length:', data.length);
          if (data.length > 0) {
            console.log('First item structure:', Object.keys(data[0]));
            console.log('First item:', JSON.stringify(data[0], null, 2));
          }
        } else if (data && typeof data === 'object') {
          console.log('Data is an object');
          if (data.data && Array.isArray(data.data)) {
            console.log('data.data is an array with length:', data.data.length);
            if (data.data.length > 0) {
              console.log('First data item structure:', Object.keys(data.data[0]));
              console.log('First data item:', JSON.stringify(data.data[0], null, 2));
            }
          }
        }

        setAssignments(data);
      } else {
        console.error(
          'Failed to fetch assignments:',
          response.status,
          response.statusText
        );
        const errorText = await response.text();
        console.error('Error response body:', errorText);
        Alert.alert('Error', `Failed to fetch assignments: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching assignments:', error);
      Alert.alert('Error', 'Failed to connect to server');
    } finally {
      setLoading(false);
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading assignments data...</Text>
        </View>
      );
    }

    return (
      <View style={styles.contentContainer}>
        <View style={styles.dataDisplayContainer}>
          <FontAwesomeIcon icon={faClipboardList} size={48} color="#007AFF" />
          <Text style={styles.dataTitle}>Assignments Data Fetched</Text>
          <Text style={styles.dataSubtitle}>
            Check the console for detailed data structure
          </Text>
          
          {assignments && (
            <View style={styles.dataPreview}>
              <Text style={styles.previewTitle}>Data Preview:</Text>
              <ScrollView style={styles.previewScroll}>
                <Text style={styles.previewText}>
                  {JSON.stringify(assignments, null, 2)}
                </Text>
              </ScrollView>
            </View>
          )}
          
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={fetchAssignmentsData}
          >
            <Text style={styles.refreshButtonText}>Refresh Data</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['top', 'left', 'right']}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <FontAwesomeIcon icon={faArrowLeft} size={18} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Assignments</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Student Name Section - Hidden in landscape mode */}
      {!isLandscape && (
        <View style={styles.studentSection}>
          <Text style={styles.studentName}>{studentName || 'Student'}</Text>
          <Text style={styles.sectionSubtitle}>Assignments & Homework</Text>
        </View>
      )}

      <View style={styles.content}>
        {isLandscape ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.scrollContainer}
          >
            {renderContent()}
          </ScrollView>
        ) : (
          <View style={styles.scrollContainer}>{renderContent()}</View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#007AFF',
    padding: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerRight: {
    width: 36,
  },
  studentSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  studentName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  contentContainer: {
    flex: 1,
  },
  dataDisplayContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  dataTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    textAlign: 'center',
  },
  dataSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
  dataPreview: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 15,
    width: '100%',
    maxHeight: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    marginBottom: 20,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  previewScroll: {
    maxHeight: 200,
  },
  previewText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
  refreshButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
